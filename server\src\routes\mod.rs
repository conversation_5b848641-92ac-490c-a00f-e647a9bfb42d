//! # 路由模块
//!
//! 定义所有HTTP路由和处理器，采用模块化DDD架构
//!
//! ## 架构设计
//! - 使用Axum Router进行路由管理
//! - 按功能模块组织路由（认证、任务、聊天等）
//! - 支持中间件和状态管理
//! - 提供WebSocket支持
//! - 参考routes.rs的完整配置实现

use app_common::logging::{ request_logging_middleware, trace_layer };
use axum::{ Router, middleware, routing::{ delete, get, post, put } };
use std::sync::Arc;
use tower::ServiceBuilder;
use tower_http::{ cors::CorsLayer, services::ServeDir };

// 处理器模块
pub mod handlers;

// 重新导出处理器
pub use handlers::*;

// 导入应用服务trait
use app_application::{
    ChatApplicationService,
    TaskApplicationService,
    UserApplicationService,
    WebSocketApplicationService,
};
use sea_orm::DatabaseConnection;

/// 应用状态结构体
///
/// 包含所有应用服务和共享资源，通过依赖注入模式提供给各个处理器
#[derive(Clone)]
pub struct AppState {
    /// 用户应用服务
    pub user_service: Arc<dyn UserApplicationService>,
    /// 任务应用服务
    pub task_service: Arc<dyn TaskApplicationService>,
    /// 聊天应用服务（基础版本）
    pub chat_service: Arc<dyn ChatApplicationService>,
    /// 弹性聊天应用服务（企业级版本，集成TASK_52搜索系统）
    pub resilient_chat_service: Option<Arc<app_application::ResilientChatApplicationService>>,
    /// WebSocket应用服务
    pub websocket_service: Arc<dyn WebSocketApplicationService>,
    /// 数据库连接
    #[allow(dead_code)]
    pub db: Arc<DatabaseConnection>,
    /// JWT密钥
    pub jwt_secret: String,
}

/// 创建认证相关路由
///
/// 包含用户注册和登录功能，无需认证即可访问
fn create_auth_routes(app_state: AppState) -> Router {
    Router::new()
        .route("/register", post(handlers::auth::register))
        .route("/login", post(handlers::auth::login))
        .with_state(app_state)
}

/// 创建需要认证的认证路由
///
/// 包含登出等需要JWT认证的认证相关功能
fn create_protected_auth_routes(app_state: AppState) -> Router {
    Router::new().route("/logout", post(handlers::auth::logout)).with_state(app_state)
}

/// 创建任务管理路由
///
/// 包含完整的任务CRUD操作，需要JWT认证
fn create_task_routes(app_state: AppState) -> Router {
    Router::new()
        .route("/tasks", get(handlers::task::get_all_tasks))
        .route("/tasks", post(handlers::task::create_task))
        .route("/tasks/{id}", get(handlers::task::get_task_by_id))
        .route("/tasks/{id}", put(handlers::task::update_task))
        .route("/tasks/{id}", delete(handlers::task::delete_task))
        .with_state(app_state)
}

/// 创建用户管理路由
///
/// 包含用户信息查询功能，需要JWT认证
fn create_user_routes(app_state: AppState) -> Router {
    Router::new()
        .route("/users/{id}", get(handlers::user::get_user_by_id))
        // 在线用户列表
        .route("/online-users", get(handlers::user::get_online_users))
        .with_state(app_state)
}

/// 创建聊天相关路由
///
/// 包含聊天室管理和消息功能，需要JWT认证
fn create_chat_routes(app_state: AppState) -> Router {
    Router::new()
        // 消息搜索和过滤
        .route("/messages/search", get(handlers::chat::get_chat_search))
        .route("/messages/chat-room/{id}", get(handlers::chat::get_chat_history))
        .with_state(app_state)
}

/// 创建WebSocket相关路由
///
/// 包含WebSocket连接和监控功能
fn create_websocket_routes(app_state: AppState) -> Router {
    Router::new()
        // WebSocket连接端点
        .route("/ws", get(handlers::websocket::websocket_handler))
        // WebSocket实时监控端点
        .route("/websocket/monitoring", get(handlers::websocket::websocket_monitoring_handler))
        // WebSocket监控端点
        .route("/websocket/stats", get(handlers::websocket::get_websocket_stats))
        .route("/websocket/connections", get(handlers::websocket::get_websocket_connections))
        .route("/websocket/metrics", get(handlers::websocket::get_websocket_metrics))
        // WebSocket稳定性端点
        .route("/websocket/stability", get(handlers::websocket::get_websocket_stability))
        .with_state(app_state)
}

/// 创建监控面板路由
///
/// 提供WebSocket实时监控面板的HTML页面访问，无需认证
fn create_monitoring_dashboard_routes() -> Router {
    Router::new()
        // WebSocket监控面板页面
        .route("/websocket-stats.html", get(handlers::monitoring::websocket_stats_page))
        // 监控面板首页重定向
        .route("/monitoring", get(handlers::monitoring::monitoring_dashboard_redirect))
}

/// 创建查询优化路由
///
/// 包含数据库查询性能优化相关功能，需要JWT认证
fn create_query_optimization_routes(app_state: AppState) -> Router {
    Router::new()
        // 单个查询优化
        .route("/query/optimize", post(handlers::query_optimization::optimize_query))
        // 批量查询优化
        .route("/query/batch-optimize", post(handlers::query_optimization::batch_optimize_queries))
        // 数据库统计信息
        .route("/query/database-stats", get(handlers::query_optimization::get_database_stats))
        // 索引推荐
        .route(
            "/query/index-recommendations",
            get(handlers::query_optimization::get_index_recommendations)
        )
        .with_state(app_state)
}

/// 创建缓存监控路由
///
/// 包含缓存命中率监控和统计功能，公开访问无需认证
fn create_cache_routes(app_state: AppState) -> Router {
    Router::new()
        // 缓存统计和监控
        .route("/cache/stats", get(handlers::cache::get_cache_stats))
        .route("/cache/stats/reset", post(handlers::cache::reset_cache_stats))
        .route("/cache/health", get(handlers::cache::get_cache_health))
        // 缓存连接池状态监控
        .route("/cache/pool", get(handlers::cache::get_cache_pool_status))
        .with_state(app_state)
}

/// 创建性能监控路由
///
/// 包含性能统计、健康检查和指标监控，公开访问无需认证
fn create_performance_routes(app_state: AppState) -> Router {
    Router::new()
        // 性能统计和监控
        .route("/performance/stats", get(handlers::health::get_performance_stats))
        .route("/performance/async-stats", get(handlers::health::get_async_performance_stats))
        .route("/performance/health", get(handlers::health::health_check))
        .route("/performance/metrics", get(handlers::health::get_detailed_metrics))
        .route("/performance/prometheus", get(handlers::health::get_prometheus_metrics))
        .route("/performance/ready", get(handlers::health::readiness_check))
        .route("/performance/live", get(handlers::health::liveness_check))
        .with_state(app_state)
}

/// 创建健康检查路由
///
/// 包含不同级别的健康检查服务，公开访问无需认证
fn create_health_routes(app_state: AppState) -> Router {
    Router::new()
        // 基础健康检查
        .route("/health", get(handlers::health::health_check))
        // 深度健康检查
        .route("/health/deep", get(handlers::health::deep_health_check))
        // 数据库连接池健康检查
        .route("/health/database", get(handlers::database_health::get_database_health))
        // 数据库连接池配置信息
        .route("/health/database/config", get(handlers::database_health::get_database_config))
        // 数据库连接池压力测试
        .route(
            "/health/database/stress-test",
            post(handlers::database_health::run_database_stress_test)
        )
        // 数据库连接池状态监控
        .route("/db/pool", get(handlers::database_health::get_database_pool_status))
        .with_state(app_state)
}

/// 创建系统监控路由
///
/// 包含系统资源监控和告警检查，公开访问无需认证
fn create_monitoring_routes(app_state: AppState) -> Router {
    Router::new()
        // 系统资源监控和告警
        .route("/monitoring/alerts", get(handlers::health::get_system_alerts))
        // 错误恢复状态监控
        .route("/error-recovery/status", get(handlers::health::error_recovery_status_handler))
        .with_state(app_state)
}

/// 创建标准Prometheus指标路由
///
/// 符合Prometheus监控系统标准，公开访问无需认证
fn create_metrics_routes(app_state: AppState) -> Router {
    Router::new()
        // 标准Prometheus指标端点
        .route("/metrics", get(handlers::health::get_prometheus_metrics))
        .with_state(app_state)
}

/// 创建需要认证的API路由
///
/// 将所有需要JWT认证的路由组合在一起，并应用认证中间件
fn create_protected_api_routes(app_state: AppState) -> Router {
    // 提取JWT密钥以避免借用检查器问题
    let jwt_secret = app_state.jwt_secret.clone();

    // 组合所有需要认证的路由
    Router::new()
        .merge(create_task_routes(app_state.clone()))
        .merge(create_user_routes(app_state.clone()))
        .merge(create_chat_routes(app_state.clone()))
        .merge(create_query_optimization_routes(app_state.clone()))
        .nest("/auth", create_protected_auth_routes(app_state.clone()))
        // 添加JWT认证中间件
        .layer(
            middleware::from_fn(
                move |mut req: axum::extract::Request, next: axum::middleware::Next| {
                    let jwt_secret = jwt_secret.clone();
                    async move {
                        // 将JWT密钥注入到请求扩展中，供AuthenticatedUser提取器使用
                        req.extensions_mut().insert(jwt_secret);
                        next.run(req).await
                    }
                }
            )
        )
}

/// 创建应用程序主路由
///
/// 组合所有业务模块路由，配置中间件栈和静态文件服务
pub fn create_routes(app_state: AppState) -> Router {
    // Favicon处理器
    let favicon_handler = || async {
        // 返回简单的SVG favicon
        (
            [("content-type", "image/svg+xml")],
            r#"<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><text y="14" font-size="16">📋</text></svg>"#,
        )
    };

    // 静态文件服务 - 从项目根目录提供静态文件
    let static_service = ServeDir::new("static");

    // 构建中间件栈
    let middleware_stack = ServiceBuilder::new()
        .layer(trace_layer())
        .layer(middleware::from_fn(request_logging_middleware))
        .layer(CorsLayer::permissive());

    // 组合所有路由并应用中间件
    Router::new()
        // Favicon路由：专门处理 /favicon.ico 请求，避免404错误
        .route("/favicon.ico", get(favicon_handler))
        // 静态文件路由：明确处理 /static/* 路径
        .nest_service("/static", ServeDir::new("static"))
        // 监控面板路由（无需认证）
        .merge(create_monitoring_dashboard_routes())
        // API路由组织
        .nest("/api", create_protected_api_routes(app_state.clone()))
        .nest("/api/auth", create_auth_routes(app_state.clone()))
        .nest("/api", create_performance_routes(app_state.clone()))
        .nest("/api", create_health_routes(app_state.clone()))
        .nest("/api", create_monitoring_routes(app_state.clone()))
        .nest("/api", create_cache_routes(app_state.clone()))
        // WebSocket路由（API路径下）
        .nest("/api", create_websocket_routes(app_state.clone()))
        // 标准Prometheus指标路由（根路径）
        .merge(create_metrics_routes(app_state))
        // 静态文件服务作为fallback，处理根路径和其他未匹配路径
        .fallback_service(static_service)
        // 应用中间件栈
        .layer(middleware_stack)
}
